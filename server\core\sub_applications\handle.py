"""
Legacy sub-application handler for backward compatibility.

This module provides backward compatibility with the old sub-application
handling system while using the new architecture underneath.
"""

from fastapi import FastAPI
from core.log import logger
from .manager import get_sub_application_manager


def handle_sub_applications(app: FastAPI):
    """
    全局处理子应用挂载 (Legacy function for backward compatibility)

    Args:
        app: FastAPI application instance
    """
    logger.info("Using legacy sub-application handler (consider migrating to new architecture)")

    # Get the sub-application manager
    manager = get_sub_application_manager()

    # Load default sub-applications if none are registered
    if not manager.registry.get_all_sub_applications():
        manager.load_default_sub_applications()

    # Mount sub-applications using the new system
    import asyncio
    asyncio.create_task(manager.mount_sub_applications(app))
