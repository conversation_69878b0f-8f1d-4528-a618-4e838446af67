from fastapi import APIRouter, Depends, Request
from apps.admin.aspect.interface_auth import CheckUserInterfaceAuth
from apps.admin.schemas.server import ServerMonitorSchema
from apps.admin.services.login import LoginService
from apps.admin.services.server import ServerService
from utils.response import ResponseUtil
from core.log.log import logger


router = APIRouter(prefix='/monitor/server', dependencies=[Depends(LoginService.get_current_user)])


@router.get(
    '', response_model=ServerMonitorSchema, dependencies=[Depends(CheckUserInterfaceAuth('monitor:server:list'))]
)
async def get_monitor_server_info(request: Request):
    # 获取全量数据
    server_info_query_result = await ServerService.get_server_monitor_info()
    logger.info('获取成功')

    return ResponseUtil.success(data=server_info_query_result)
