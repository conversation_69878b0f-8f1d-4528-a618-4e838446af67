"""
Cache manager for unified cache operations.

This module provides a high-level cache manager that abstracts
different cache backends and strategies.
"""

from typing import Any, Optional, Union
from datetime import timedelta
from core.container import singleton
from .redis_manager import RedisManager, get_redis_manager


@singleton()
class CacheManager:
    """
    Unified cache manager.
    
    Provides a high-level interface for cache operations
    that can work with different cache backends.
    """
    
    def __init__(self, redis_manager: RedisManager = None):
        self.redis_manager = redis_manager or get_redis_manager()
    
    async def get(self, key: str) -> Any:
        """Get value from cache."""
        return await self.redis_manager.get(key)
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache."""
        return await self.redis_manager.set(key, value, expire)
    
    async def delete(self, *keys: str) -> int:
        """Delete keys from cache."""
        return await self.redis_manager.delete(*keys)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        return await self.redis_manager.exists(key)
    
    async def health_check(self) -> dict:
        """Perform cache health check."""
        return await self.redis_manager.health_check()


# Global cache manager instance
_cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance."""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager
