"""
Exception handlers for the FastAPI application.

This module provides centralized exception handling functionality.
"""

from typing import Dict, Any
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.exceptions import HTTPException
from fastapi.responses import JSONResponse
from core.log import logger
from .base import BaseApplicationException
from .business import *


class ExceptionHandlerManager:
    """Manager for exception handlers."""
    
    def __init__(self, app: FastAPI):
        self.app = app
        self.register_handlers()
    
    def register_handlers(self):
        """Register all exception handlers."""
        
        # Handle application exceptions
        @self.app.exception_handler(BaseApplicationException)
        async def application_exception_handler(request: Request, exc: BaseApplicationException):
            logger.error(f"Application exception: {exc}")
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.to_dict()
            )
        
        # Handle HTTP exceptions
        @self.app.exception_handler(HTTPException)
        async def http_exception_handler(request: Request, exc: HTTPException):
            logger.warning(f"HTTP exception: {exc.detail}")
            return JSONResponse(
                status_code=exc.status_code,
                content={
                    "message": exc.detail,
                    "status_code": exc.status_code
                }
            )
        
        # Handle general exceptions
        @self.app.exception_handler(Exception)
        async def general_exception_handler(request: Request, exc: Exception):
            logger.exception(f"Unhandled exception: {exc}")
            return JSONResponse(
                status_code=500,
                content={
                    "message": "Internal server error",
                    "status_code": 500
                }
            )


# Global exception handler manager
_exception_handler_manager: ExceptionHandlerManager = None


def register_exception_handlers(app: FastAPI) -> ExceptionHandlerManager:
    """Register exception handlers for the FastAPI app."""
    global _exception_handler_manager
    if _exception_handler_manager is None:
        _exception_handler_manager = ExceptionHandlerManager(app)
    return _exception_handler_manager


def get_exception_handler_manager() -> ExceptionHandlerManager:
    """Get the global exception handler manager."""
    return _exception_handler_manager
