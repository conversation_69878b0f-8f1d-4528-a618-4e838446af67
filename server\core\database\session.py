"""
Database session management.

This module provides session management utilities for database operations,
including dependency injection integration with FastAPI.
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends
from core.container import get_container
from .manager import DatabaseManager, get_database_manager
from .base import DatabaseBase


class DatabaseSession:
    """Database session wrapper for dependency injection."""

    def __init__(self, manager: DatabaseManager = None):
        self.manager = manager or get_database_manager()

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session for dependency injection."""
        async with self.manager.get_session() as session:
            yield session

    async def get_transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with transaction for dependency injection."""
        async with self.manager.get_transaction() as session:
            yield session


# Global session instance
_db_session: DatabaseSession = None


def get_database_session_instance() -> DatabaseSession:
    """Get global database session instance."""
    global _db_session
    if _db_session is None:
        _db_session = DatabaseSession()
    return _db_session


# FastAPI dependency functions
async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for getting database session.

    Usage:
        @app.get("/users/")
        async def get_users(db: AsyncSession = Depends(get_database_session)):
            # Use db session here
            pass
    """
    db_session = get_database_session_instance()
    async with db_session.get_session() as session:
        yield session


async def get_database_transaction() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency for getting database session with transaction.

    Usage:
        @app.post("/users/")
        async def create_user(db: AsyncSession = Depends(get_database_transaction)):
            # Use db session with automatic transaction management
            pass
    """
    db_session = get_database_session_instance()
    async with db_session.get_transaction() as session:
        yield session


# Backward compatibility functions
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Backward compatibility function for existing code."""
    async for session in get_database_session():
        yield session


async def init_create_table():
    """Initialize database tables (backward compatibility)."""
    manager = get_database_manager()
    await manager.create_tables()


# Legacy imports for backward compatibility
Base = DatabaseBase
