from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase
from urllib.parse import quote_plus
from config.settings import DataBaseConfig


# 定义数据库基类，用于继承
class Base(AsyncAttrs, DeclarativeBase):
    pass


# 构建异步SQLAlchemy数据库URL
ASYNC_SQLALCHEMY_DATABASE_URL = (
    'postgresql+asyncpg://' if DataBaseConfig.db_type == 'postgresql' else 'mysql+asyncmy://'
    f'{DataBaseConfig.db_username}:{quote_plus(DataBaseConfig.db_password)}@'
    f'{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}'
)

# 创建异步数据库引擎
async_engine = create_async_engine(
    ASYNC_SQLALCHEMY_DATABASE_URL,
    echo=DataBaseConfig.db_echo,
    max_overflow=DataBaseConfig.db_max_overflow,
    pool_size=DataBaseConfig.db_pool_size,
    pool_recycle=DataBaseConfig.db_pool_recycle,
    pool_timeout=DataBaseConfig.db_pool_timeout,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(autocommit=False, autoflush=False, bind=async_engine)


# 定义获取数据库会话函数
async def get_db():
    async with AsyncSessionLocal() as current_db:
        yield current_db


# 定义初始化数据库结构函数
async def init_create_table():
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
