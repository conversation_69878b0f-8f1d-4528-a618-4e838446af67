"""
Unified configuration management using Pydantic Settings.

This module provides type-safe configuration classes with automatic
environment variable loading and validation.
"""

import os
from functools import lru_cache
from typing import Literal, Optional
from pydantic import computed_field, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class BaseConfig(BaseSettings):
    """Base configuration class with common settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


class AppSettings(BaseConfig):
    """Application core configuration."""
    
    # Application settings
    app_env: str = Field(default="dev", description="Application environment")
    app_name: str = Field(default="RuoYi-FastAPI", description="Application name")
    app_root_path: str = Field(default="/dev-api", description="Application root path")
    app_host: str = Field(default="0.0.0.0", description="Application host")
    app_port: int = Field(default=9099, description="Application port")
    app_version: str = Field(default="1.0.0", description="Application version")
    app_reload: bool = Field(default=True, description="Enable hot reload")
    app_ip_location_query: bool = Field(default=True, description="Enable IP location query")
    app_same_time_login: bool = Field(default=True, description="Allow same time login")
    
    # Debug settings
    debug: bool = Field(default=False, description="Debug mode")
    
    model_config = SettingsConfigDict(
        env_prefix="APP_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )


class DatabaseSettings(BaseConfig):
    """Database configuration."""
    
    db_type: Literal["mysql", "postgresql"] = Field(default="mysql", description="Database type")
    db_host: str = Field(default="127.0.0.1", description="Database host")
    db_port: int = Field(default=3306, description="Database port")
    db_username: str = Field(default="root", description="Database username")
    db_password: str = Field(default="1234", description="Database password")
    db_database: str = Field(default="ruoyi-fastapi", description="Database name")
    
    # Connection pool settings
    db_echo: bool = Field(default=True, description="Enable SQL echo")
    db_max_overflow: int = Field(default=10, description="Max overflow connections")
    db_pool_size: int = Field(default=50, description="Connection pool size")
    db_pool_recycle: int = Field(default=3600, description="Pool recycle time")
    db_pool_timeout: int = Field(default=30, description="Pool timeout")
    
    @computed_field
    @property
    def sqlglot_parse_dialect(self) -> str:
        """Get SQLGlot dialect for the database type."""
        return "postgres" if self.db_type == "postgresql" else self.db_type
    
    @computed_field
    @property
    def database_url(self) -> str:
        """Generate database URL."""
        driver = "postgresql+asyncpg" if self.db_type == "postgresql" else "mysql+asyncmy"
        return f"{driver}://{self.db_username}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_database}"
    
    model_config = SettingsConfigDict(
        env_prefix="DB_",
        **BaseConfig.model_config.__dict__
    )


class RedisSettings(BaseConfig):
    """Redis configuration."""
    
    redis_host: str = Field(default="127.0.0.1", description="Redis host")
    redis_port: int = Field(default=6379, description="Redis port")
    redis_username: Optional[str] = Field(default=None, description="Redis username")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_database: int = Field(default=0, description="Redis database")
    
    # Connection settings
    redis_max_connections: int = Field(default=50, description="Max connections")
    redis_socket_timeout: int = Field(default=5, description="Socket timeout")
    redis_socket_connect_timeout: int = Field(default=5, description="Connect timeout")
    
    @computed_field
    @property
    def redis_url(self) -> str:
        """Generate Redis URL."""
        auth = ""
        if self.redis_username and self.redis_password:
            auth = f"{self.redis_username}:{self.redis_password}@"
        elif self.redis_password:
            auth = f":{self.redis_password}@"
        
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_database}"
    
    model_config = SettingsConfigDict(
        env_prefix="REDIS_",
        **BaseConfig.model_config.__dict__
    )


class JwtSettings(BaseConfig):
    """JWT configuration."""
    
    jwt_secret_key: str = Field(
        default="b01c66dc2c58dc6a0aabfe2144256be36226de378bf87f72c0c795dda67f4d55",
        description="JWT secret key"
    )
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_expire_minutes: int = Field(default=1440, description="JWT expiration time in minutes")
    jwt_redis_expire_minutes: int = Field(default=30, description="JWT Redis expiration time")
    
    model_config = SettingsConfigDict(
        env_prefix="JWT_",
        **BaseConfig.model_config.__dict__
    )


class GenSettings(BaseConfig):
    """Code generation configuration."""
    
    gen_author: str = Field(default="ruoyi", description="Code author")
    gen_package_name: str = Field(default="com.ruoyi", description="Package name")
    gen_auto_remove_pre: bool = Field(default=False, description="Auto remove prefix")
    gen_table_prefix: str = Field(default="sys_", description="Table prefix")
    
    model_config = SettingsConfigDict(
        env_prefix="GEN_",
        **BaseConfig.model_config.__dict__
    )


class UploadSettings(BaseConfig):
    """Upload configuration."""
    
    upload_path: str = Field(default="./vf_admin/upload_path", description="Upload path")
    upload_prefix: str = Field(default="/profile", description="Upload URL prefix")
    default_allowed_extension: list[str] = Field(
        default=["bmp", "gif", "jpg", "jpeg", "png", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "html", "htm", "txt"],
        description="Allowed file extensions"
    )
    
    @computed_field
    @property
    def upload_absolute_path(self) -> str:
        """Get absolute upload path."""
        return os.path.abspath(self.upload_path)
    
    model_config = SettingsConfigDict(
        env_prefix="UPLOAD_",
        **BaseConfig.model_config.__dict__
    )


@lru_cache()
def get_settings() -> tuple[AppSettings, DatabaseSettings, RedisSettings, JwtSettings, GenSettings, UploadSettings]:
    """
    Get all configuration settings with caching.
    
    Returns:
        Tuple of all configuration objects
    """
    return (
        AppSettings(),
        DatabaseSettings(),
        RedisSettings(),
        JwtSettings(),
        GenSettings(),
        UploadSettings()
    )


# Global configuration instances
app_settings, db_settings, redis_settings, jwt_settings, gen_settings, upload_settings = get_settings()
