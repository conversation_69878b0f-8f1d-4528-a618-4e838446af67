"""
Database management module for the FastAPI application.

This module provides:
- Database connection management with connection pooling
- Session management with proper lifecycle handling
- Transaction management with automatic rollback
- Database model base classes
- Migration support
"""

from .manager import DatabaseManager, get_database_manager
from .session import get_database_session, DatabaseSession
from .base import DatabaseBase
from .connection import DatabaseConnection

__all__ = [
    "DatabaseManager",
    "get_database_manager",
    "get_database_session",
    "DatabaseSession",
    "DatabaseBase",
    "DatabaseConnection"
]