"""
Legacy middleware handler for backward compatibility.

This module provides backward compatibility with the old middleware
handling system while using the new architecture underneath.
"""

from fastapi import FastAPI
from core.log import logger
from .manager import get_middleware_manager


def handle_middleware(app: FastAPI):
    """
    全局中间件处理 (Legacy function for backward compatibility)

    Args:
        app: FastAPI application instance
    """
    logger.info("Using legacy middleware handler (consider migrating to new architecture)")

    # Get the middleware manager
    manager = get_middleware_manager()

    # Load default middlewares if none are registered
    if not manager.registry.get_all_middlewares():
        manager.load_default_middlewares()

    # Setup middlewares using the new system
    import asyncio
    asyncio.create_task(manager.setup_middlewares(app))
