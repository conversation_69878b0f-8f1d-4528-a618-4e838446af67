"""
Cache management module for the FastAPI application.

This module provides:
- Redis connection management
- Cache abstraction layer
- Multi-level caching strategies
- Cache decorators for easy usage
- Cache invalidation strategies
"""

from .manager import CacheManager, get_cache_manager
from .strategies import CacheStrategy, TTLStrategy, LRUStrategy
from .decorators import cached, cache_invalidate, cache_key
from .redis_manager import RedisManager

__all__ = [
    "CacheManager",
    "get_cache_manager",
    "CacheStrategy",
    "TTLStrategy",
    "LRUStrategy",
    "cached",
    "cache_invalidate",
    "cache_key",
    "RedisManager"
]