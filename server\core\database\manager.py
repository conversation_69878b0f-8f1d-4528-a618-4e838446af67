"""
Database manager for handling database operations.

This module provides a high-level database manager that handles
connection management, session lifecycle, and transaction management.
"""

from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker
from core.container import singleton
from core.config import get_database_config
from .connection import DatabaseConnection, get_database_connection
from .base import DatabaseBase


@singleton()
class DatabaseManager:
    """
    Database manager for handling all database operations.
    
    Provides:
    - Connection management
    - Session lifecycle management
    - Transaction management
    - Database initialization
    """
    
    def __init__(self, connection: DatabaseConnection = None):
        self.connection = connection or get_database_connection()
        self.config = get_database_config()
        self._session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    @property
    def session_factory(self) -> async_sessionmaker:
        """Get or create session factory."""
        if self._session_factory is None:
            self._session_factory = async_sessionmaker(
                bind=self.connection.engine,
                class_=AsyncSession,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
        return self._session_factory
    
    async def initialize(self):
        """Initialize database manager."""
        if self._initialized:
            return
        
        # Test connection
        if not await self.connection.test_connection():
            raise RuntimeError("Failed to connect to database")
        
        self._initialized = True
    
    async def create_tables(self):
        """Create all database tables."""
        if not self._initialized:
            await self.initialize()
        
        async with self.connection.engine.begin() as conn:
            await conn.run_sync(DatabaseBase.metadata.create_all)
    
    async def drop_tables(self):
        """Drop all database tables."""
        if not self._initialized:
            await self.initialize()
        
        async with self.connection.engine.begin() as conn:
            await conn.run_sync(DatabaseBase.metadata.drop_all)
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session with automatic cleanup.
        
        Yields:
            AsyncSession: Database session
        """
        if not self._initialized:
            await self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    @asynccontextmanager
    async def get_transaction(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session with transaction management.
        
        Automatically commits on success or rolls back on error.
        
        Yields:
            AsyncSession: Database session with transaction
        """
        async with self.get_session() as session:
            async with session.begin():
                try:
                    yield session
                except Exception:
                    await session.rollback()
                    raise
    
    async def execute_raw_sql(self, sql: str, parameters: dict = None):
        """
        Execute raw SQL statement.
        
        Args:
            sql: SQL statement to execute
            parameters: Optional parameters for the SQL statement
            
        Returns:
            Result of the SQL execution
        """
        async with self.get_session() as session:
            result = await session.execute(sql, parameters or {})
            await session.commit()
            return result
    
    async def health_check(self) -> dict:
        """
        Perform database health check.
        
        Returns:
            Dictionary with health check results
        """
        try:
            # Test basic connection
            connection_ok = await self.connection.test_connection()
            
            # Test session creation
            session_ok = False
            try:
                async with self.get_session() as session:
                    await session.execute("SELECT 1")
                    session_ok = True
            except Exception:
                pass
            
            return {
                "status": "healthy" if connection_ok and session_ok else "unhealthy",
                "connection": connection_ok,
                "session": session_ok,
                "database_info": self.connection.get_connection_info()
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "connection": False,
                "session": False
            }
    
    async def close(self):
        """Close database manager and cleanup resources."""
        if self._session_factory:
            # Close all sessions in the factory
            await self._session_factory.close_all()
        
        await self.connection.close()
        self._initialized = False
        self._session_factory = None


# Global database manager instance
_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get global database manager instance."""
    global _manager
    if _manager is None:
        _manager = DatabaseManager()
    return _manager


async def reset_database_manager():
    """Reset database manager (mainly for testing)."""
    global _manager
    if _manager:
        await _manager.close()
    _manager = None
