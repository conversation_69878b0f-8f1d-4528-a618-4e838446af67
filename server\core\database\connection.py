"""
Database connection management.

This module provides database connection utilities and configuration.
"""

from typing import Optional
from urllib.parse import quote_plus
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine
from sqlalchemy.pool import QueuePool
from core.config import get_database_config


class DatabaseConnection:
    """Database connection manager."""
    
    def __init__(self, config=None):
        self.config = config or get_database_config()
        self._engine: Optional[AsyncEngine] = None
    
    @property
    def engine(self) -> AsyncEngine:
        """Get or create database engine."""
        if self._engine is None:
            self._engine = self._create_engine()
        return self._engine
    
    def _create_engine(self) -> AsyncEngine:
        """Create database engine with proper configuration."""
        database_url = self._build_database_url()
        
        engine_kwargs = {
            "echo": self.config.db_echo,
            "pool_size": self.config.db_pool_size,
            "max_overflow": self.config.db_max_overflow,
            "pool_timeout": self.config.db_pool_timeout,
            "pool_recycle": self.config.db_pool_recycle,
            "poolclass": QueuePool,
        }
        
        # Add database-specific configurations
        if self.config.db_type == "mysql":
            engine_kwargs.update({
                "connect_args": {
                    "charset": "utf8mb4",
                    "autocommit": False,
                }
            })
        elif self.config.db_type == "postgresql":
            engine_kwargs.update({
                "connect_args": {
                    "server_settings": {
                        "application_name": "ruoyi-fastapi",
                    }
                }
            })
        
        return create_async_engine(database_url, **engine_kwargs)
    
    def _build_database_url(self) -> str:
        """Build database URL from configuration."""
        # URL encode password to handle special characters
        encoded_password = quote_plus(self.config.db_password)
        
        if self.config.db_type == "postgresql":
            driver = "postgresql+asyncpg"
        elif self.config.db_type == "mysql":
            driver = "mysql+asyncmy"
        else:
            raise ValueError(f"Unsupported database type: {self.config.db_type}")
        
        return (
            f"{driver}://"
            f"{self.config.db_username}:{encoded_password}@"
            f"{self.config.db_host}:{self.config.db_port}/"
            f"{self.config.db_database}"
        )
    
    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            async with self.engine.begin() as conn:
                await conn.execute("SELECT 1")
            return True
        except Exception:
            return False
    
    async def close(self):
        """Close database connection."""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
    
    def get_connection_info(self) -> dict:
        """Get connection information (without sensitive data)."""
        return {
            "db_type": self.config.db_type,
            "db_host": self.config.db_host,
            "db_port": self.config.db_port,
            "db_database": self.config.db_database,
            "db_username": self.config.db_username,
            "pool_size": self.config.db_pool_size,
            "max_overflow": self.config.db_max_overflow,
        }


# Global connection instance
_connection: Optional[DatabaseConnection] = None


def get_database_connection() -> DatabaseConnection:
    """Get global database connection instance."""
    global _connection
    if _connection is None:
        _connection = DatabaseConnection()
    return _connection


def reset_database_connection():
    """Reset database connection (mainly for testing)."""
    global _connection
    if _connection:
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, schedule the close operation
                loop.create_task(_connection.close())
            else:
                # If no loop is running, run the close operation
                asyncio.run(_connection.close())
        except Exception:
            pass  # Ignore errors during cleanup
    _connection = None
